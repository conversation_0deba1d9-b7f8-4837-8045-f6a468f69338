// Simple Carousel Styles
.simple-carousel-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  margin: 0 auto;
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;

  @media (max-width: 768px) {
    height: 200px;
  }
}

.simple-carousel-inner {
  position: relative;
  width: 100%;
  height: 100%;
}

.simple-carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    opacity: 1;
    z-index: 1;
  }
}

.simple-carousel-media {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.simple-carousel-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 18px;
  display: none; /* Hide on all devices */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  &.prev {
    left: 10px;
  }

  &.next {
    right: 10px;
  }
}

.simple-carousel-dots {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  padding: 5px 10px;

  @media (max-width: 768px) {
    padding: 4px 8px;
    gap: 6px;
  }
}

.simple-carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 0;

  &.active {
    background-color: white;
    transform: scale(1.2);
  }

  @media (max-width: 768px) {
    width: 8px;
    height: 8px;
  }
}
