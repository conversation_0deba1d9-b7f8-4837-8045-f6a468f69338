@import 'colors';
@import 'fonts';

// Home
.overlay {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.weight-box {
  align-items: center;
  background-color: $light-gray;
  border: .06rem solid $litterpic-green;
  border-radius: .5rem;
  box-shadow: .3rem .3rem .6rem $translucent-black;
  display: flex;
  font-family: 'Arial', serif;
  font-weight: bold;
  height: auto;
  justify-content: center;
}

.pounds-text {
  color: $black;
  font-size: 2rem;
}

p {
  &.litter-weight {
    color: $litterpic-green;
    font-family: Poppins, sans-serif;
    font-size: 2rem;
    padding: 1rem;
  }

  .index-inspire-change-text {
    color: $litterpic-green;
    font-style: italic;
    font-weight: bold;
  }
}

.index-wrapper {
  column-gap: 5rem;
  display: grid;
  grid-template-columns: 1.25fr 1.75fr;

  p {
    font-size: 1rem;
  }

  .index-starting-text {
    color: $litterpic-green;
    font-size: 2.15rem;
    font-weight: bolder;
  }

  .index-column-two-row-one {
    display: grid;
    grid-template-rows: auto;
    margin-right: 1rem;

    ul {
      display: flex;
      flex-direction: column;
      padding: 0;
    }

    li {
      flex: 1;
    }
  }
}

.ambassador-wrapper {
  column-gap: 3rem;
  display: grid;
  grid-template-columns: 1.4fr 2fr;
}

.ambassador-paragraph {
  line-height: 1.5;
}

.ambassador-heading-text-with-icon {
  display: flex;
  flex-direction: row;
}

.image-row {
  margin-bottom: 2rem;
}

.index-more-stories-button {
  display: inline;
  margin-left: 1rem;
  position: relative;
  text-decoration: none;
  top: -5px;

  button {
    border-radius: .5rem;
    display: inline;
    transition: background-color .5s ease;

    &:hover {
      background-color: $facebook-blue;
    }
  }
}

.home-carousel-section {
  align-items: center;
  display: grid;
  grid-template-columns: .7fr 1fr;;

  &-text {
    color: $table-highlight-green;
    display: inline-block;
    font-size: 2rem;
    letter-spacing: 2px;
    line-height: 1.5;
    margin-left: 2.25rem;
    max-width: calc(100%);
    word-wrap: break-word;
  }
}

// Carousel
.carousel-container {
  height: 300px;
  overflow: hidden;
  position: relative;
  width: 100%;
  touch-action: pan-x; /* Enable horizontal swiping */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */

  @media (max-width: 768px) {
    touch-action: pan-x; /* Ensure horizontal swiping works on mobile */
    user-select: none; /* Prevent text selection during swipe */
  }
}

.carousel-slide {
  height: 100%;
  overflow: hidden;
  position: relative;
}

.carousel-page {
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  transition: opacity 0.8s ease; /* Faster transition for better mobile experience */
  width: 100%;
  z-index: 1;

  &.active {
    opacity: 1;
    z-index: 2; /* Ensure active slide is on top */
  }

  @media (max-width: 768px) {
    transition: opacity 0.5s ease; /* Even faster on mobile */
  }
}

.carousel-media {
  display: flex;
  width: auto;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  object-fit: contain;
}

.carousel-dots {
  align-items: center;
  bottom: 10px;
  display: flex;
  justify-content: center;
  left: 50%;
  opacity: .8;
  position: absolute;
  transform: translateX(-50%);
  z-index: 3;
  width: auto; /* Changed from default 100% to auto */
  max-width: 80%; /* Limit maximum width */
}

.carousel-dot {
  background-color: $light-gray;
  border-radius: 50%;
  cursor: pointer;
  height: 10px;
  margin: 0 3px; /* Reduced from 5px to 3px */
  width: 10px;

  &.active {
    background-color: $facebook-blue;
    opacity: 1;
  }
}

@media (max-width: 76.5rem) {
  .ambassador-heading-text-with-icon {
    display: flex;
    flex-direction: row;
    margin-right: 30%;
  }

  .carousel-media {
    display: flex;
    width: auto;
    height: 200px;
    margin-left: auto;
    margin-right: auto;
    object-fit: contain;
  }

  .index-wrapper, .ambassador-wrapper {
    column-gap: 5rem;
    display: flex;
    flex-direction: column;

    p,
    li {
      font-size: 1rem;
      margin-bottom: 1rem;
    }

    .index-starting-text {
      color: $litterpic-green;
      font-size: 1.5rem;
      font-weight: bolder;
    }

    .index-column-two-row-one {
      display: flex;
      flex-direction: column;
      margin-right: 1rem;

      ul {
        display: flex;
        flex-direction: column;
        padding: 0;
      }

      li {
        flex: 1;
        margin-left: 1rem;
      }
    }
  }

  .index-more-stories-button {
    display: flex;
    justify-content: flex-start;
    margin-left: 0;
    padding-left: 0;

    button {
      font-size: .75rem;
      margin-bottom: 1rem;
      margin-top: 1rem;
    }
  }

  .pounds-text {
    color: $black;
    font-size: 1rem;
  }

  p {
    &.litter-weight {
      color: $litterpic-green;
      font-size: 1rem;
      padding: 1rem;
    }
  }

  .home-bottom-carousel-section {
    display: none;
  }

  .home-carousel-section {
    align-items: center;
    display: flex;
    flex-direction: row;

    &-text {
      font-size: .9rem;
      letter-spacing: 1px;
      line-height: 1.25;
      margin-left: 1rem;
      max-width: calc(100% - 5rem);
    }
  }

  // Carousel
  .carousel-container {
    height: 150px;
    margin-bottom: 2rem;
    margin-left: 1rem;
    margin-top: 2rem;
    width: 500px;
  }

  .carousel-dots {
    width: auto;
    max-width: 70%;
    gap: 2px; /* Reduce gap between dots */
  }

  .carousel-dot {
    height: 4px;
    margin: 0 2px;
    width: 4px;
  }
}

@media (max-width: 102.25rem) and (orientation: landscape) {
  .pounds-text {
    color: $black;
    font-size: 1.75rem;
  }

  p {
    &.litter-weight {
      font-size: 1.75rem;
      padding: 1rem;
    }
  }
}


.qr-section {
  margin-bottom: 1.5rem;

  .app-text {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin-bottom: 2rem;
    text-align: center;

    .head-and-icon {
      display: flex;
      margin-bottom: .5rem;
      align-items: center;
      text-align: center;
      gap: .5rem;

      .heading-text {
        margin-top: 0;
        margin-bottom: 0;
      }

      img {
        width: 40px;
        height: 40px;
        border-radius: 22%;
      }
    }
  }

  .qr-group {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;

    .qr-container {
      align-items: center;
      display: flex;
      justify-content: space-evenly;
      text-align: center;
      width: calc(50% - 1px);
      border-right: 1px solid $disabled-gray;

      &:last-child {
        border-right: none;
      }

      > img {
        height: auto;
        width: 50%;
        max-width: 11rem;
        transform: scale(.9);
      }

      .qr-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 0 2rem;

        > a img {
          height: auto;
          width: 100%;
          max-width: 11.5rem;
          min-width: 133px;
          margin-top: 1rem;
          transform: scale(.9);
        }
      }
    }
  }
}

@media (max-width: 45.5rem) {
  .qr-group {
    flex-direction: column;
    align-items: center;

    .qr-container {
      width: 100%;
      margin-bottom: 2rem; /* Reduced margin */
      border-right: none !important;

      > img {
        min-width: 120px; /* Smaller QR code */
        max-width: 120px; /* Limit maximum size */
        transform: scale(0.8); /* Slightly smaller */
      }

      .qr-box {
        padding: 0 1rem; /* Reduced padding */

        p {
          font-size: 0.9rem; /* Smaller text */
          margin: 0.5rem 0; /* Reduced margin */
        }

        > a img {
          max-width: 140px; /* Smaller app store badges */
          min-width: 120px; /* Smaller minimum width */
          transform: scale(0.8); /* Slightly smaller */
        }
      }
    }
  }
}

@media (max-width: 793px) {

  .qr-section .qr-group .qr-container .qr-box  {
    padding: 0 1rem;

  }
  .qr-section .app-text .head-and-icon .heading-text {
    text-align: center;
  }
}


