@import 'colors';
@import 'fonts';

.logo-bar,
.logo-bar-left-content,
.logo-bar-right-content,
.logo-content {
  align-items: flex-start;
  display: flex;

  .notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: auto;
    margin-bottom: auto;
    margin-right: 25px;
    height: 100%;
  }

  .logo-content {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

    img {
      border-radius: .5rem;
      cursor: pointer;
      height: 3.5rem;
      object-fit: cover;
      width: auto;
    }

    .logo-text {
      align-items: flex-start;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      margin-left: .5rem;

      p {
        line-height: 1;
      }

      .logo {
        font-size: 1.5rem;
      }

      .tagline {
        color: $litterpic-green;
        font-size: .9rem;
        font-style: italic;
      }
    }
  }


}

.logo-bar {
  background-color: $white;
  justify-content: space-between;
  padding: 1rem;
}

.login-button,
.donate-button {
  align-items: center;
  align-self: center;
  background-color: $facebook-blue;
  border-radius: .5rem;
  color: $white;
  cursor: pointer;
  display: flex;
  font-family: Rubik, Times, sans-serif;
  font-size: 16px;
  height: auto;
  justify-content: center;
  margin-bottom: auto;
  padding: .25rem .25rem;
  text-align: center;
  text-decoration: none;
  transition: background-color .3s ease;
  width: 150px;

  &:hover {
    background-color: $nav-green;
    color: $black;
  }
}

.donate-button {
  margin-bottom: auto;
  margin-right: 25px;
  margin-top: auto;
  width: 100px;
}

.login-button {
  margin-left: .5rem;
}

.profile-picture-wrapper {
  align-items: center;
  display: flex;
  position: relative;
}

.profile-picture {
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
  height: 50px;
  object-fit: cover;
  width: 50px;
}

.dropdown-menu {
  display: flex;
  flex-direction: column;
  margin-top: .5rem;
  position: absolute;
  right: 2rem;
  z-index: 1;

  button {
    &:hover {
      background-color: $white;
      border: 1px solid;
      color: $litterpic-green;
      font-weight: bold;
    }

  }
}

.login-menu {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: calc(5%);
}

.login-menu-button {
  &:hover {
    background-color: $white;
    border: 1px solid;
    color: $litterpic-green;
    font-weight: bold;
  }
}

@media (max-width: 76.5rem) {
  .login-button {
    display: none;
  }

  .donate-button {
    font-size: .75rem;
    font-weight: bold;
    height: 2.25rem;
    margin-bottom: 0;
    margin-top: 0;
    width: 75px;
  }

  .logo-bar {
    align-self: flex-start;
  }

  .logo-text {
    flex-direction: column;
    margin-left: .5rem;

    .logo {
      font-size: 14px;
    }

    .tagline {
      font-size: 9px;
    }
  }

  .logo-bar-right-content {
    flex-direction: row;
    height: 100%;
    margin-right: 2rem;
    padding-left: 30px;
  }

  .profile-picture {
    margin-left: 5.5rem;
  }

  .profile-dropdown {
    display: none;
  }
}
