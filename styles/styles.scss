@tailwind base;
@tailwind components;
@tailwind utilities;

// Component and Page Styles
@import 'about';
@import 'announcement';
@import 'banner';
@import 'colors';
@import 'contact';
@import 'delete-account';
@import 'dontate';
@import 'edit-profile';
@import 'fonts';
@import 'footer';
@import 'home';
@import 'homeimagerow';
@import 'icons';
@import 'login';
@import 'logobar';
@import 'navbar';
@import 'privacy';
@import 'profile';
@import 'report';
@import 'signup';
@import 'stories';
@import 'verify-email';
@import 'volunteer';
@import 'community_service';
@import 'notification';
@import 'modern-stories';
@import 'loading-spinner';
@import 'skeleton';
@import 'mobile-optimizations';
@import 'simple-carousel';
@import 'member-stats';
@import 'members';


//Font Awesome
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css');
//Rubik Medium
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@500&display=swap');
//Poppins (replaces DIN)
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');


// styles.css
* {
  margin: 0;
  padding: 0;
}

body {
  background-color: $white;
  font-family: Poppins, sans-serif;
}

main {
  min-height: 100vh;
}


h1 {
  font-family: Rubik, Times, sans-serif;

  &.heading-text {
    color: $litterpic-green;
    font-size: 3rem;
    margin-bottom: 2.5rem;
    text-align: left;
  }
}

h2 {
  font-family: Rubik, Times, sans-serif;
  font-weight: bold;

  &.heading-text {
    color: $litterpic-green;
    margin-bottom: .3rem;
    margin-top: 2.5rem;
    text-align: left;
  }
}

p {
  font-size: 1rem;
  line-height: 1.5;
}

li {
  margin-bottom: 1rem;
}

ol {
  margin-left: 2.5rem;
}

button {
  background-color: $litterpic-green;
  border: 0;
  color: $white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: .5rem;

  &:hover {
    background-color: $facebook-blue;
  }

  &:disabled {
    cursor: not-allowed;

    /* Override the hover effect when the button is disabled */
    &:hover {
      background-color: $disabled-gray;
    }
  }
}

label {
  padding-bottom: .3rem;
  padding-top: .3rem;
}

form {
  padding: .7rem;
}

input,
select,
textarea {
  font-family: Poppins, sans-serif;
  font-size: 16px;
  height: 2rem;
  margin-bottom: 1rem;
}

.page {
  align-items: flex-start;
  background-color: $light-gray;
  display: flex;
  justify-content: center;
  min-height: 100vh;
  padding: 1.5rem;
}

.content {
  background-color: $white;
  box-shadow: 0 .125rem .25rem $translucent-black;
  margin-left: auto;
  margin-right: auto;
  min-height: 35vh;
  padding: 1.5rem;
  width: 80%;
}

// Media Query for Mobile Devices
@media (max-width: 76.5rem) {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  .page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: $light-gray;
  }

  p {
    font-size: 1.125rem;
    line-height: 1.5;
  }

  li,
  a {
    font-size: 1.125rem;
    line-height: 1.5;
  }

  h1 {
    &.heading-text {
      color: $litterpic-green;
      font-size: 1.875rem;
      margin-bottom: 2.5rem;
      text-align: left;
    }
  }

  .content {
    background-color: $white;
    box-shadow: 0 .125rem .25rem $translucent-black;
    margin: 0;
    padding: .3rem;
    width: 100%;
  }

  button {
    padding: .25rem;
  }
}

// Media Query for Landscape
@media (max-width: 102.25rem) and (orientation: landscape) {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    margin: 0 auto;
  }

  p,
  li {
    font-size: .9rem;
  }

  .content {
    box-shadow: 0 .25rem .5rem $translucent-black;
    margin: 0;
  }
}
