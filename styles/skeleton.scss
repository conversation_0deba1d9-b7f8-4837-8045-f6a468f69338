@import 'colors';

// Skeleton loading animation
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.post-skeleton {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1.5rem;
  
  &-header {
    display: flex;
    padding: 1rem;
    align-items: center;
  }
  
  &-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    margin-right: 0.75rem;
  }
  
  &-user-info {
    flex: 1;
  }
  
  &-username {
    height: 14px;
    width: 120px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  
  &-meta {
    height: 10px;
    width: 80px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }
  
  &-image {
    width: 100%;
    height: 0;
    padding-bottom: 75%; // 4:3 aspect ratio
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  &-actions {
    display: flex;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
  }
  
  &-action {
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    margin-right: 1rem;
  }
  
  &-content {
    padding: 1rem;
  }
  
  &-line {
    height: 10px;
    width: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 8px;
    
    &.short {
      width: 70%;
    }
  }
}

// Initial loading container
.initial-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: rgba(245, 245, 245, 0.5);
  border-radius: 8px;
  margin: 1rem 0;
  min-height: 300px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(1, 94, 65, 0.2);
    border-radius: 50%;
    border-top-color: $litterpic-green;
    animation: spin 1s ease-in-out infinite;
  }
  
  p {
    margin-top: 1rem;
    color: $litterpic-green;
    font-weight: 500;
    font-size: 1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
