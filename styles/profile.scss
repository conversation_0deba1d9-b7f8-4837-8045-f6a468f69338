@import 'colors';
@import 'fonts';

.profile-content {
  display: flex;
}

h1.heading-text.profile-heading {
  margin-bottom: 1rem;
}

.profile-page-picture {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: 1fr .1fr;
  margin-left: auto;
  margin-right: .5rem;

  img {
    border: 2px solid $litterpic-green;
    border-radius: 50%;
    grid-column: 1;
    grid-row: 1;
    height: 200px;
    justify-self: center;
    object-fit: cover;
    width: 200px;
  }
}

.edit-profile-button {
  align-self: flex-start;
  border-radius: .5rem;
  color: $white;
  cursor: pointer;
  font-size: 1rem;
  grid-column: 1;
  grid-row: 2;
  justify-self: center;
  margin-top: .5rem;
  width: 200px;

  &::before {
    white-space: normal;
  }
}

.delete-account-profile-link {
  align-self: flex-start;
  border-radius: .5rem;
  color: $dark-blue; /* Make sure the $white variable is defined elsewhere in your CSS */
  cursor: pointer;
  font-size: .75rem;
  font-style: italic;
  grid-column: 1;
  grid-row: 2;
  justify-self: center;
  margin-top: 3rem;
  text-decoration: none; /* Removes the underline from the link */

  &::before {
    white-space: normal;
  }

  &:hover {
    color: $red;
  }
}

.profile-info {
  display: grid;
  grid-template-columns: .5fr 2fr;
  padding-left: .5rem;
}

.profile-item {
  color: $profile-text-gray;
  font-size: .75rem;
  font-weight: bold;
  padding-top: 1rem;
}

.profile-value {
  font-size: 1rem;
  justify-content: flex-start;
  padding-left: 1rem;
  padding-top: 1rem;
}

.member-profile-info {
  display: grid;
  grid-template-columns: .5fr 2fr;
  padding-left: .5rem;
  row-gap: 1rem;
}

.member-profile-item {
  color: $profile-text-gray;
  font-size: .75rem;
  font-weight: bold;
  justify-content: end;
  margin-bottom: auto;
  margin-top: auto;
}

.member-profile-value {
  font-size: 1rem;
  justify-content: flex-start;
}

.ambassador {
  align-items: center;
  display: inline-flex;
  margin-bottom: 1rem;

  &-text {
    font-size: 1rem;
  }
}


@media (max-width: 76.5rem) {
  .profile-page-picture {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 1fr .1fr;
    margin-left: auto;
    margin-right: .5rem;

    img {
      border: 2px solid $litterpic-green;
      border-radius: 50%;
      grid-column: 1;
      grid-row: 1;
      height: 100px;
      justify-self: center;
      object-fit: cover;
      width: 100px;
    }
  }

  .edit-profile-button {
    align-self: flex-start;
    font-size: 1rem;
    grid-column: 1;
    grid-row: 2;
    justify-self: center;
    width: 100px;

    &::before {
      white-space: normal;
    }
  }

  .member-profile-info {
    display: grid;
    grid-template-columns: 1.5fr 2fr;
    padding-left: .5rem;
    row-gap: 1rem;
  }

  .member-profile-item {
    color: $profile-text-gray;
    font-size: .75rem;
    font-weight: bold;
    justify-content: end;
    margin-bottom: auto;
    margin-top: auto;
  }

  .member-profile-value {
    font-size: 1rem;
    justify-content: flex-start;
    margin-left: 1rem;
  }
}

