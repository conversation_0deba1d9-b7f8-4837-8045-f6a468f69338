@import 'colors';
@import 'fonts';

// Footer
.address-link {
  &:hover {
    color: $dark-blue;
    cursor: pointer;
  }
}

.footer {
  align-items: end;
  background-color: $gray;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding-bottom: .5rem;
  padding-top: .5rem;

  &-column-1,
  &-column-2,
  &-column-3 {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    justify-self: center;

    p,
    a {
      display: flex;
      font-size: 16px;
      line-height: 1.5;
      margin: 0;
      padding: 0;
      text-align: center;
      text-decoration: none;
    }
  }

}

@media (max-width: 76.5rem) {
  .footer {
    &-column-1,
    &-column-2,
    &-column-3 {
      p,
      a {
        font-size: 12px;
        line-height: 1.15;
      }
    }
  }
}
