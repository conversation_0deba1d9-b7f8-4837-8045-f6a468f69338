@import 'colors';
@import 'fonts';

.page {
  display: flex;
  justify-content: center;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;

  .no-notifications-message {
    font-size: 1.5rem;
    color: #757575;
    text-align: center;
    margin-top: 20px;
  }

  .notif-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 10px;
    box-sizing: border-box;

    .notifications-list {
      margin-top: 20px;
    }

    .notification-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 15px;
      background-color: #ffffff;
      border-radius: 10px;
      margin-bottom: 20px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;

      &.unread {
        background-color: #e8f5e9;
      }

      &:hover {
        background-color: #e0f2f1;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
      }

      .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: $litterpic-green;
        cursor: pointer;
        margin-right: 10px;
      }

      &.read .status-indicator {
        background-color: #808080;
      }

      .delete-notification-button {
        background-color: rgba(255, 255, 255, 0.7);
        color: $litterpic-green;
        border: none;
        padding: 5px;
        cursor: pointer;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        margin-left: 10px;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;

        &:hover {
          color: #d32f2f;
          background-color: rgba(255, 255, 255, 0.9);
        }
      }

      .notification-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        text-align: left;
        flex-grow: 1;
        width: calc(100% - 40px);
        overflow: hidden;
        padding-right: 10px;

        .notification-time {
          font-size: 0.75rem;
          color: #757575;
          width: 100px;
          text-align: center;
          margin-left: 10px;
          margin-right: 10px;
          word-wrap: break-word;
          flex-shrink: 0;
        }

        .notification-message {
          font-size: 10pt;
          margin: 0;
          color: #333;
          font-weight: 500;
          word-wrap: break-word;
          overflow-wrap: break-word;
          flex-grow: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }
      }
    }
  }
}


@media (max-width: 76.5rem) {
  .notif-content {
    max-width: 90%;

    .notification-item {
      position: relative;
      padding-right: 45px;

      .notification-content {
        flex-direction: column;
        align-items: flex-start;
        width: calc(100% - 10px);
        max-width: calc(100% - 45px);
      }

      .delete-notification-button {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 1.2rem;
        padding: 5px;
        margin: 0;
        background-color: transparent;
      }

      .notification-time {
        width: 100%;
        text-align: left;
        margin: 0 0 5px 0;
        font-size: 0.7rem;
        word-wrap: break-word;
      }

      .notification-message {
        font-size: 0.9rem;
        width: 100%;
        margin-bottom: 5px;
      }

      .status-indicator {
        position: absolute;
        top: 15px;
        left: 10px;
      }
    }
  }
}

@media (max-width: 400px) {
  .notif-content {
    padding: 0 5px;

    .notification-item {
      padding: 10px 45px 10px 25px;
      border-radius: 5px;
      margin-bottom: 10px;

      .status-indicator {
        width: 8px;
        height: 8px;
        top: 12px;
        left: 8px;
      }

      .notification-content {
        padding-left: 5px;
      }

      .notification-time {
        font-size: 0.65rem;
        margin-bottom: 3px;
      }

      .notification-message {
        font-size: 0.85rem;
        line-height: 1.3;
        width: 100%;
        max-width: calc(100% - 10px);
        padding-right: 5px;
      }

      .delete-notification-button {
        top: 8px;
        right: 8px;
        padding: 3px;
        font-size: 1rem;
        z-index: 2;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
