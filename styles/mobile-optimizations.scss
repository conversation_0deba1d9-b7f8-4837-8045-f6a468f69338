// Mobile optimizations

// Improve touch targets for better mobile interaction
@media (max-width: 768px) {
  // Fix for the hamburger menu
  .nav-toggle {
    padding: 15px;
    margin-right: 10px;

    .icon {
      svg {
        height: 30px;
        width: 30px;
      }
    }
  }

  // Fix for the navigation menu
  .nav-links {
    a, button {
      padding: 15px;
      width: 100%;
      text-align: center;
      border-bottom: 1px solid rgba(0,0,0,0.1);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  // Fix for the post grid
  .post-grid {
    margin: 0 -10px;
  }

  .post-grid-column {
    padding: 0 10px;
  }

  // Fix for the post
  .post {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  // Fix for the carousel
  .post-carousel {
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
  }

  // Fix for the "See More Stories" button
  .custom-file-button {
    width: 90%;
    max-width: 200px;
    margin: 5px auto;
    font-size: 1.1rem;
    display: block;
  }
}

// Fix for iOS Safari specific issues
@supports (-webkit-touch-callout: none) {
  // Fix for the 300ms tap delay
  * {
    touch-action: manipulation;
  }
}
