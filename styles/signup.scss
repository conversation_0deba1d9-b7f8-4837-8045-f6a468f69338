@import 'colors';
@import 'fonts';

// Login
.signup-form {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  max-width: 400px;
}

.sign-in-form {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.migrated-user-forgot-password {
  font-style: italic;
  font-weight: normal;
  text-decoration: none;
}

.signup-email,
.signup-password,
.signup-confirm-password {
  background-color: $input;
  font-size: 1.1rem;
  margin: .5rem auto;
  position: relative;
  width: 15.6rem;
}

.signup-password-container {
  align-items: center;
  display: flex;
  position: relative;
}

.signup-button {
  border-radius: .5rem;
  font-family: Pop<PERSON>s, sans-serif;
  margin: 1.9rem auto 0;
  width: 6.25rem;

  &:disabled {
    background-color: $gray;
    color: $profile-text-gray;
    cursor: not-allowed;
  }
}

.error-container {
  align-items: center;
  display: flex;
  margin-top: .5rem;
}

.error-message {
  color: $red;
  font-size: 1rem;
  font-weight: bolder;
  margin-right: 8px;
  text-align: center;
}

.signup-legal {
  font-size: .65rem;
  font-style: italic;
  padding-top: .75rem;

  a {
    font-size: .65rem;
    font-style: italic;
  }
}

.valid-password-attribute {
  color: $litterpic-green;
}

.invalid-password-attribute {
  color: $red;
}

.signup-password-requirements {
  font-weight: bold;

  p {
    font-size: .75rem;
  }
}