@import 'colors';
@import 'fonts';

.contact-wrapper {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
}

.contact-container {
  background: linear-gradient(to bottom, $litterpic-green, $black);
  border-radius: .5rem;
  width: 80%;
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 70%;
  color: $white;

  h1 {
    font-size: 1.5rem; // Base size for smaller screens

    @media (min-width: 768px) {
      font-size: 2rem; // Medium screens
    }

    @media (min-width: 1024px) {
      font-size: 2.4rem; // Larger screens
    }
  }
}

.contact-form-group {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  margin-right: 3.75rem;

  label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
}

.required {
  font-size: smaller;
  vertical-align: super;
}

#message {
  height: 5rem;
  width: 100%;
}

.contact-form-row {
  align-items: center;
  column-gap: .6rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.contact-phone-email-social {
  align-items: center;
  column-gap: .6rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin-left: .6rem;
  margin-top: .6rem;
}

.contact-lets-chat {
  font-size: 2.5rem;
  font-weight: bold;
  margin-left: .6rem;
}

.contact-heading {
  font-weight: bold;
}

#contact-submit-button {
  display: inline-block;
  padding: 10px 20px;
  //background-color: #0070f3;
  //color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: .6rem;

  &:hover {
    background-color: $facebook-blue;
  }
}

.contact-submit-response {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 1.25rem;
}

@media (max-width: 76.5rem) {
  // Contact
  .contact-wrapper {
    align-items: center;
    display: flex;
    height: 70%;
    justify-content: center;
  }

  #message {
    height: 3.75rem;
    width: 100%;
  }

  .contact-container {
    background: linear-gradient(to bottom, $litterpic-green, $black);
    border-radius: .5rem;
    color: $white;
    display: flex;
    flex-direction: column;
    font-family: Times, serif;
    font-size: 1rem;
    font-weight: bold;
    height: 70%;
    padding: .6rem;
    width: 100%;

    p {
      font-size: 1rem;
    }

    h1 {
      text-align: center;
    }

    label {
      font-family: Times, serif;
      font-size: 1rem;
      font-weight: bold;
    }
  }

  .contact-form-group {
    display: flex;
    flex-direction: column;
    margin-right: 0;

    label {
      margin-bottom: .3rem;
    }
  }

  .required {
    font-size: smaller;
    vertical-align: super;
  }

  .contact-input {
    font-size: 1.5rem;
    height: 3rem;
    width: 25rem;
  }

  .contact-textarea {
    height: 15rem;
    width: 50%;
  }

  .contact-form-row {
    align-items: center;
    column-gap: 0;
    display: grid;
    grid-template-columns: 1fr;
  }

  .contact-phone-email-social {
    align-items: center;
    display: grid;
    grid-template-columns: 1fr;
    margin-left: .6rem;
    margin-top: .6rem;
    row-gap: .6rem;
  }

  .contact-lets-chat {
    font-size: 1.875rem;
    font-weight: normal;
    margin-left: .6rem;
  }

  .contact-heading {
    p {
      font-family: Times, serif;
      font-size: 1rem;
      font-weight: normal;
    }
  }

  #contact-submit-button {
    border-radius: .5rem;
    display: flex;
    margin-top: .6rem;
  }

  .contact-submit-response {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 1.25rem;
  }

  #firstName,
  #lastName,
  #email {
    width: 60%;
  }
}

@media (max-width: 102.25rem) and (orientation: landscape) {
  .contact-container {
    height: 100%;
    padding: .6rem;
    width: 100%;

    button {
      padding: .3rem;
    }
  }

  .contact-wrapper {
    margin-bottom: 65%;
  }

  #firstName,
  #lastName,
  #email {
    width: 60%;
  }

  #message {
    width: 100%;
  }

  #contact-submit-button {
    border-radius: .5rem;
    display: flex;
    margin-top: .6rem;
  }
}