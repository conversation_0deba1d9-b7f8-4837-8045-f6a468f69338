.image-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    margin-bottom: 2.5rem;

    /* Style for Next.js Image component */
    > span {
        position: relative !important;
        border-radius: 10% !important;
        height: 17.2rem !important;
        width: 12.5rem !important;
        margin: .5rem !important;
        overflow: hidden !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        transition: transform 0.3s ease, box-shadow 0.3s ease !important;
        animation: fadeIn 0.5s ease-in !important;

        &:hover {
            transform: scale(1.02) !important;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
        }

        @media (max-width: 600px) {
            flex: 1 1 calc(50% - 1rem) !important;
        }

        img {
            object-fit: cover !important;
            border-radius: 10% !important;
        }
    }

    img {
        border-radius: 10% !important;
        height: 17.2rem !important;
        margin: .5rem !important;
        width: 12.5rem !important;
        object-fit: cover !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        transition: transform 0.3s ease, box-shadow 0.3s ease !important;

        /* Add loading animation */
        animation: fadeIn 0.5s ease-in !important;

        &:hover {
            transform: scale(1.02) !important;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
        }

        @media (max-width: 600px) {
            flex: 1 1 calc(50% - 1rem) !important;
        }
    }
}

/* Loading animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Specific style for community service images */
.community-service-image {
    border-radius: 10% !important;
    object-fit: cover !important;
}

/* Container for Next.js Image component */
.image-container {
    position: relative;
    width: 12.5rem;
    height: 17.2rem;
    margin: 0.5rem;
    border-radius: 10%;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite, fadeIn 0.5s ease-in;

    &:hover {
        transform: scale(1.02);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    @media (max-width: 600px) {
        flex: 1 1 calc(50% - 1rem);
    }
}

@keyframes shine {
    to {
        background-position-x: -200%;
    }
}
