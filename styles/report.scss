@use 'colors';
@use 'fonts';

.label-row,
.input-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.input- {
  &row {
    margin-bottom: 1rem;
  }

  &smaller {
    height: 1.75rem;
  }
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;

  th, td {
    border: 1px solid #ccc;
    padding: 8px;
    text-align: left;
  }

  th {
    text-align: center;
    background-color: $litterpic-green;
    color: white;
  }
}

.report- {
  &td-weight {
    text-align: center !important;
  }

  &total-weight {
    display: flex;
    font-size: 1.5rem;
    font-weight: bold;
    justify-content: center;
    margin-top: 1.5rem;
  }

  &weight-value {
    color: $litterpic-green;
    font-weight: bolder;
    margin-left: .5rem;
    margin-right: .15rem;
  }

  &submit-button {
    border-radius: .5rem;
    margin-left: 1.25%;
  }

  &reset-button {
    border-radius: .5rem;
    margin-left: 1.25%;
  }

  &scrollable-dropdown {
    max-height: 150px;
    overflow-y: auto;
    direction: ltr;
  }

  &no-data-message {
    font-size: 1.5rem;
    font-weight: bolder;
    text-align: center;
  }

  &top-litter-pickers {
    font-family: Poppins, sans-serif;
    font-size: 1.75rem;
  }

  &form-group {
    margin-bottom: 1rem;
    padding: 0;
  }

  &label-mobile {
    display: none;
  }

  &is-loading {
    display: flex;
    font-size: 1.5rem;
    font-weight: bold;
    justify-content: center;
  }

  &form-label,
  &form-select {
    text-align: center;
  }

  // end report-
}


.interactive- {
  &litter-stats-header {
    font-family: Poppins, sans-serif;
    font-size: 1.75rem;
    font-weight: bold;
  }

  &litter-stats-header {
    margin-bottom: 1rem;
  }

  // end interactive-
}


/* For WebKit browsers like Chrome and Safari */
input::-webkit-input-placeholder {
  color: #333;
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  padding-left: .15rem;
}

/* For Mozilla Firefox */
input::-moz-placeholder {
  color: #333;
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  padding-left: .15rem;
}

/* For Internet Explorer */
input:-ms-input-placeholder {
  color: #333;
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  padding-left: .15rem;
}

/* For Microsoft Edge */
input::-ms-input-placeholder {
  color: #333;
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  padding-left: .15rem;
}

/* Standard syntax */
input::placeholder {
  color: #333;
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  padding-left: .15rem;
}

.date-label-mobile {
  display: none;
}

.grid-layout {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-gap: 0 10px;
}

.leaderboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 16px;
  margin-top: 1rem;
  margin-bottom: 3rem;

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    border: 1px solid #ccc;
    text-align: center;
    padding: 8px;
  }

  th {
    background-color: $litterpic-green;
    color: white;
  }

  // end leaderboard-grid
}


@media (max-width: 768px) {
  .leaderboard-grid th,
  .leaderboard-grid td {
    font-size: 1rem;
    border: 1px solid #ccc;
    text-align: center;
    padding: 3px;
  }

  .report- {
    &form-label {
      display: none;
    }

    &label-mobile {
      display: block;
      align-self: flex-start;
      margin-left: 2%;
    }

    &optional-text {
      color: grey;
      font-size: 12px;
      margin-top: -1.5rem;
      margin-bottom: 2rem;
      margin-left: 1.5rem;
    }

    &td-weight {
      text-align: center !important;
    }

    &total-weight {
      font-size: 1.2rem;
    }

    &submit-button {
      font-weight: bold;
      margin-left: 2.5%;
    }

    &reset-button {
      font-weight: bold;
      margin-left: 2.5%;
    }

    // end mobile .report-
  }


  .input-row {
    display: grid;
    grid-template-columns: auto auto;
    gap: 0.25rem;
  }

  .results-table th,
  .results-table td {
    font-size: 10px;
    padding: 3px;
  }
}
