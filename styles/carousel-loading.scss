@import 'colors';

.carousel-container {
  &.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    
    &:after {
      content: '';
      width: 40px;
      height: 40px;
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid $litterpic-green;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
