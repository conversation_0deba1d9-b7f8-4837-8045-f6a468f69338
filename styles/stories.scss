@import 'colors';
@import 'fonts';

.stories-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stories-top-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.refresh-cache-button {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 8px 12px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #e0e0e0;
  }
}

.create-post-button {
  border-radius: .5rem;

  button {
    align-self: flex-end;
    margin-bottom: 2.5rem;
  }

  &:hover {
    background-color: $facebook-blue;
  }
}

.stories-about-us {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 3rem;
}

.search-and-filter-image {
  border-radius: .5rem;
  height: 350px;
  object-fit: fill;
  width: 100%;
}

.search-and-filter-input-button-container {
  align-items: flex-start;
  display: flex;
  margin-bottom: 2rem;
  margin-left: 1rem;
  justify-content: flex-start;

  .show-all-posts-button,
  .show-my-posts-button,
  .post-search-button {
    border-radius: .5rem;
    margin-left: 1rem;
  }

  .post-search-input {
    margin-left: 10px;
    margin-top: 15px;
    border-radius: .5rem;
    width: 500px;
  }
}

.search-and-filter-button-container {
  align-items: center;
  display: flex;
  max-width: 95%;
}

.post-search-input {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.post-grid {
  display: flex;
  gap: .5rem;
}

.post {
  border: .01rem solid $litterpic-green;
  border-radius: .5rem;
  margin-bottom: 1rem;
  position: relative;
}

.likes-comments {
  display: flex;
  justify-content: center;
  margin-bottom: .5rem;

  .likes-comments-likes-field {
    margin-right: 2rem;
    position: relative;
  }

  .likes-comments-comment-field {
    margin-left: 2rem;
  }
}

.like-list-container {
  font-size: 12px;
  left: 50%;
  position: absolute;
  text-align: start;
  top: 100%;
  margin-top: -2px;
  transform: translateX(-50%);
  z-index: 1000;
  min-width: 150px;

  // Create invisible bridge to prevent popup from disappearing
  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    height: 12px;
    background: transparent;
  }
}

.like-list {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 8px 12px 12px 12px;
  border: 1px solid #ddd;
  background-color: white;
  min-width: 180px;
  max-width: 250px;
}

.like-user {
  display: block;
  margin-top: .5rem;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-photo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.user-name {
  flex-grow: 1;
  text-decoration: none;
  cursor: pointer;

  &.disabled {
    cursor: default;
    color: #999;
    text-decoration: none;

    &:hover {
      color: #999;
    }
  }
}

// Comment user styling
.comment-user {
  &.clickable {
    color: $litterpic-green;
    text-decoration: none;
    cursor: pointer;
    font-weight: 600;

    &:hover {
      color: darken($litterpic-green, 15%);
      text-decoration: underline;
    }
  }
}

.comment-user-avatar {
  &.clickable {
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }
}

.like-date {
  margin-left: 10px;
}

.like-user.loading {
  opacity: 0;
}

.like-user.loaded {
  opacity: 1;
  transition: opacity 0.5s ease;
}

.like-count,
.comment-count {
  margin-left: 5px;
}

.fa-comment {
  margin-left: 10px;
}

.filled-comment {
  color: $litterpic-green;
}

.filled-heart {
  color: $red;
}

.empty-heart {
  svg {
    border: 2px solid $red;
  }
}

.story-comment-input {
  display: flex;
  flex-direction: column;

  .comment-submit-button {
    border-radius: .5rem;
    margin-bottom: 1rem;
    margin-left: auto;
    margin-right: auto;
    width: 30%;
  }

  .comment-text-input {
    font-size: 1rem;
    height: 60px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 2px;
    width: 90%;
  }
}

.comment {
  align-items: center;
  display: grid;
  gap: 10px;
  grid-template-columns: auto 1fr;
  margin-bottom: 10px;

  img {
    align-self: flex-start;
    border-radius: 50%;
    grid-row: span 2;
    height: 30px;
    margin-left: .5rem;
    width: 30px;
  }

  .comment-time {
    font-size: .6rem;
  }

  .comment-text {
    font-size: .8rem;
    grid-column: 2;

    .comment-user {
      font-weight: bold;
      margin-right: 5px;
    }
  }
}

.suggestion-item {
  background-color: $white;
  cursor: pointer;
  padding: .5rem;

  &:hover {
    background-color: $litterpic-green;
  }

  &.active {
    background-color: $litterpic-green;
    color: $white;
  }
}

.post-carousel {
  border: 1px $litterpic-green;
  display: flex;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8px;
  max-width: 100%;
  box-sizing: border-box;
  touch-action: pan-y; /* Enable horizontal swiping */
  cursor: grab; /* Show grab cursor to indicate swipeable */
  user-select: none; /* Prevent text selection during drag */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  /* Fixed aspect ratio container to prevent layout shifts */
  width: 100%;
  height: 0;
  padding-bottom: 75%; /* 4:3 aspect ratio */
  position: relative;

  &:active {
    cursor: grabbing; /* Change cursor when actively swiping */
  }

  /* Create a container for the media to allow for smooth transitions */
  .carousel-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit
.carousel-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  transition: opacity 0.3s ease;
  display: block;
}

// Modern dot indicators
.carousel-dots {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  z-index: 10;
  padding: 5px 12px;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 20px;
  width: auto;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none; /* Ensure dots don't interfere with swiping */
}

.carousel-counter {
  color: white;
  font-size: 12px;
  margin-right: 8px;
  font-weight: 500;
}

.carousel-swipe-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  animation: fadeInOut 4s ease-in-out forwards;
  pointer-events: none;
  z-index: 20;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  text-align: center;

  &::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M9.5 11c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5zm0 5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm4.5-9.5c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5zm0 5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm5-12c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm0 3.5c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5zm0 5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z'/%3E%3C/svg%3E");
    background-size: contain;
    vertical-align: middle;
    margin-right: 8px;
  }
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* Loading spinner styles */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  width: 100%;

  p {
    margin-top: 1rem;
    color: $litterpic-green;
    font-weight: 500;
    font-size: 1rem;
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(1, 94, 65, 0.2);
  border-radius: 50%;
  border-top-color: $litterpic-green;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.8);
  margin: 0 2px;

  &.active {
    background-color: white;
    transform: scale(1.3);
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
  }
}

.carousel-swipe-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  animation: fadeInOut 4s ease-in-out forwards;
  pointer-events: none;
  z-index: 20;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 30px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  }
}

.post-username-location {
  display: grid;
  grid-template-columns: 7% 38% 55%;
  justify-content: space-between;
  padding-left: 2rem;
  padding-top: 1rem;
}

.post-time {
  align-items: flex-start;
  display: flex;
  font-size: .75rem;
  grid-column: 3;
  grid-row: 2;
  justify-content: flex-end;
  margin-right: 10px;
}

a.post-user-name {
  grid-column: 2;
  grid-row: 1;
}

.follow-button {
  grid-column: 1 / span 2;
  grid-row: 3;
  background-color: $litterpic-green;
  border: 0;
  color: $white;
  border: none;
  border-radius: .5rem;
  cursor: pointer;
  font-family: Poppins, sans-serif;
  font-size: .75rem;
  font-weight: lighter;
  margin-bottom: 5px;
  margin-top: 5px;
  width: auto;
  padding: 0.15rem 0.3rem;
  text-align: center;
  text-decoration: none;
  z-index: 1000;
  justify-self: start;
}

.follow-button.following {
  background-color: transparent;
  color: $litterpic-green;
  font-style: italic;
  font-weight: bold;
  margin-top: 0;
}

.follow-button.following:hover {
  color: red;
  background-color: yellow;
}

.profile-image {
  align-content: flex-end;
  display: flex;
  justify-content: flex-start;
  grid-column: 1;
  grid-row: 1 / span 2;

  img {
    border-radius: 50%;
    height: 40px;
    object-fit: fill;
    width: 40px;
  }
}

.post-ambassador {
  align-content: flex-start;
  display: flex;
  font-weight: normal;
  justify-content: flex-start;
  flex-direction: row;
  grid-column: 2/span 3;
  grid-row: 2;

  margin-bottom: 5px;

  &-text {
    font-size: .7rem;
  }
}

.post-user-name {
  align-items: flex-start;
  color: $black;
  display: flex;
  flex-direction: column;
  font-weight: bold;
  justify-content: flex-end;
  margin-left: 5px;
  text-decoration: none;

  :hover {
    color: $litterpic-green;
    transform: scale(1.1);
  }
}

.post-location {
  align-items: flex-end;
  color: $dark-blue;
  display: flex;
  font-size: .75rem;
  justify-content: flex-end;
  margin-right: 10px;
  grid-column: 3;
  grid-row: 1;

  :hover {
    color: $litterpic-green;
    transform: scale(1.05);
  }
}

.post-title {
  font-size: 1.5em;
  font-weight: bolder;
  margin-left: 1rem;
  margin-top: 1.25rem;
}

.post-litter-weight-collected {
  font-weight: bold;
  margin-bottom: .5rem;
  margin-top: 1.25rem;
  text-align: center;
}

.post-description {
  font-family: inherit;
  font-size: 1rem;
  height: auto;
  overflow-wrap: anywhere;
  padding: 1rem 2rem;
  width: auto;
  word-break: break-word;

  & a {
    color: #007bff;
    text-decoration: underline;
  }

  & a:hover {
    color: #0056b3;
  }
}

.recent-location-select {
  font-size: .9rem;
  width: 605px;
}

.create-post-content {
  display: flex;
  justify-content: center;

  button {
    border-radius: .5em;
    font-size: 1rem;
    font-weight: normal;
    margin-top: .5rem;

    &:hover {
      background-color: $facebook-blue;
    }
  }

  img {
    margin: 5px;
    max-height: 300px;
    max-width: 20vw;
    object-fit: cover;
  }

  input {
    border: black solid 1px;
    border-radius: .5rem;
    font-size: .9rem;
    width: 600px;
  }

  /* Hide input arrows - For Chrome, Safari, Edge, Opera */
  .no-increment-decrement::-webkit-outer-spin-button,
  .no-increment-decrement::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .hint-placeholder::placeholder {
    color: #888;
    font-size: 14px;
    opacity: 0.8;
  }

  /* Hide input arrows - For Firefox */
  .no-increment-decrement[type=number] {
    -moz-appearance: textfield;
  }

  textarea {
    border: black solid 1px;
    border-radius: .5rem;
    cursor: text;
    font-size: .9rem;
    height: 200px;
    line-height: 1.5;
    padding: 5px;
    width: 600px;
  }
}

.litter-container {
  margin-bottom: 9px;

  .radio-buttons {
    display: flex;
    justify-content: flex-start;

    label {
      align-items: center;
      display: flex;
      margin-right: 12px;

      input[type="radio"] {
        appearance: none;
        border: 2px solid $litterpic-green;
        border-radius: 50%;
        cursor: pointer;
        height: 20px;
        margin-right: 6px;
        outline: none;
        width: 20px;

        &:checked {
          background-color: $litterpic-green;
        }
      }

      &:last-child {
        margin-right: 0;
      }

      span {
        font-size: 14px;
      }
    }
  }
}

.create-post-limit-message {
  color: $litterpic-green;
  font-size: .7rem;
  margin-bottom: 1rem;
  margin-left: .5rem;
  margin-top: .25rem;
}

.create-post-file-input {
  margin: 0;
  padding: 0;
}

.custom-file-button {
  background-color: $litterpic-green;
  border: 0;
  border-radius: .5rem;
  color: $white;
  cursor: pointer;
  display: inline-block;
  font-family: Poppins, sans-serif;
  font-size: 0.9rem;
  padding: .2rem .4rem;
  text-align: center;
  text-decoration: none;
  max-width: 180px;
  margin: 10px auto;

  &:hover {
    background-color: $facebook-blue;
  }

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: 0.15rem 0.3rem;
    max-width: 150px; /* Smaller on mobile */
    margin: 8px auto; /* Center the button */
  }
}

.meatball-menu {
  cursor: pointer;
  float: right;
  margin-right: .5rem;
  margin-top: .5rem;
}

.grayed-out {
  color: $dark-gray;
  cursor: not-allowed;
  font-size: 1rem;
  margin-left: .5rem;
}

.post-dropdown-menu {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: none;
  left: 78%;
  padding: 8px 0;
  position: absolute;
  right: 0;
  text-align: left;
  top: 6%;
  z-index: 1000;
  min-width: 160px;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  li {

    &:not(.grayed-out) {
      cursor: pointer;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.4;
      margin: 0;
      padding: 10px 16px;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }
    }

    &:hover {
      background-color: #f8f9fa;
      color: $litterpic-green;
    }

    &.grayed-out {
      color: #999;
      cursor: not-allowed;
      font-size: 0.875rem;
      margin: 0;
      padding: 10px 16px;
      opacity: 0.6;
    }
  }

  &.show {
    display: inline-block;
    padding: 8px 0;
    min-width: 160px;
  }
}

// Special styling for report options
.meatball-post-menu {
  li {
    font-size: 0.875rem !important;
    padding: 12px 16px !important;
    margin: 0 !important;
    color: #333; // Same color for all options

    &:hover {
      background-color: #f8f9fa;
      color: $litterpic-green;
    }
  }
}

.back-to-top-button {
  background-color: $litterpic-green;
  border: 0;
  color: $white;
  border: none;
  border-radius: .5rem;
  cursor: pointer;
  font-family: Poppins, sans-serif;
  font-size: 0.9rem;
  margin-left: auto;
  padding: 0.2rem 0.4rem;
  text-align: center;
  text-decoration: none;
  z-index: 1000;
  max-width: 150px;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: 0.15rem 0.3rem;
    max-width: 120px; /* Smaller on mobile */
  }
}

.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.comment-text-content a {
  color: blue;
  text-decoration: underline;
}


@media (max-width: 76.5rem) {
  .profile-image {
    justify-content: center;
    padding-right: 0;
  }

  .post-username-location {
    display: grid;
    grid-template-columns: 15% 55% 30%;
    justify-content: space-between;
    padding-left: .5rem;
    padding-right: .5rem;
    padding-top: 1rem;
  }

  .post-user-name {
    display: flex;
    font-size: .7rem;
    justify-content: center;
  }

  .post-location {
    a {
      font-size: .5rem;
    }
  }

  .post-time {
    font-size: .5rem;
  }

  .post-ambassador {
    font-weight: normal;
    justify-content: flex-start;
    flex-direction: row;
    grid-column: 2;
    grid-row: 2;

    &-text {
      font-size: .5rem;
      margin-top: 4px;
    }
  }

  .search-and-filter-image {
    height: 150px;
    width: 100%;
  }

  .search-and-filter-input-button-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .show-all-posts-button,
    .show-my-posts-button,
    .post-search-button,
    .post-search-input {
      font-size: 14px;
      height: 3rem;
      margin-left: .25rem;
      margin-right: .25rem;
      width: 100px;
    }

    .post-search-input {
      text-align: center;
      font-size: 14px;
      height: 2rem;
      margin: .25rem;
      width: 175px;
    }
  }

  .post {
    border: .01rem solid $litterpic-green;
    border-radius: .5rem;
    margin-bottom: .5rem;
  }

  .carousel-image {
    height: auto;
    max-height: 350px;
    object-fit: contain;
    overflow: hidden;
    width: auto;
    max-width: 100%;
    margin: 0 auto;
  }

  .carousel-dots {
    bottom: 10px;
  }

  .carousel-dot {
    width: 6px;
    height: 6px;
  }

  .post-dropdown-menu {
    left: 63%;
    min-width: 140px;
    font-size: 0.8rem;

    li {
      padding: 8px 12px !important;
      font-size: 0.8rem !important;
    }
  }

  .recent-location-select {
    width: 350px;
  }

  .create-post-content {
    display: flex;
    justify-content: center;

    img {
      max-width: 99vw;
      object-fit: cover;
    }

    input {
      border-style: inset;
      border-width: 1px;
      border-color: initial;
      width: 80vw;
    }

    textarea {
      height: 100px;
      width: 80vw;
    }
  }

  .create-post-limit-message {
    font-size: .6rem;
  }

  .litter-container {
    margin-bottom: 9px;

    .radio-buttons {
      display: flex;
      justify-content: flex-start;

      label {
        align-items: center;
        display: flex;
        margin-right: 12px;

        input[type="radio"] {
          appearance: none;
          border: 2px solid $litterpic-green;
          border-radius: 50%;
          cursor: pointer;
          height: 20px;
          margin-right: 6px;
          outline: none;
          width: 20px;

          &:checked {
            background-color: $litterpic-green;
          }
        }

        &:last-child {
          margin-right: 0;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }
}


@media (max-width: 102.25rem) and (orientation: landscape) {
  .profile-image {
    justify-content: center;
    padding-right: 0;
  }

  .post-username-location {
    align-content: flex-start;
    display: grid;
    //grid-template-columns: 7% 38% 55%;
    justify-content: space-between;
    padding-left: .5rem;
    padding-right: .5rem;
    padding-top: 1rem;
  }

  .post-user-name {
    align-items: flex-start;
    display: flex;
    font-size: .75rem;
    justify-content: flex-start;
  }

  .post-location {
    a {
      font-size: .5rem;
    }
  }

  .carousel-image {
    height: auto;
    max-height: 400px;
    object-fit: contain;
    overflow: hidden;
    width: auto;
    max-width: 100%;
    margin: 0 auto;
  }

  .recent-location-select {
    width: 460px;
  }

  //make sure it's in alphabetical order

  .litter-container {
    align-items: flex-start;
    display: flex;
    flex-direction: column;

    input {
      border: 1px solid #ccc;
      border-radius: 5px;
      margin-bottom: 9px;
      padding: 10px;
      width: 100%;
    }

    .radio-buttons {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 9px;


      label {
        align-items: center;
        display: flex;
        margin-right: 12px;

        input[type="radio"] {
          appearance: none;
          border: 2px solid $litterpic-green;
          border-radius: 50%;
          cursor: pointer;
          height: 20px;
          margin-right: 6px;
          outline: none;
          width: 20px;

          &:checked {
            background-color: $litterpic-green;
          }
        }

        &:last-child {
          margin-right: 0;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }
}