@use 'colors';

.members-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

.members-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid $litterpic-green;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
  
  p {
    color: #666;
    font-size: 1.1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.members-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid $litterpic-green;
  
  .back-button {
    background-color: $litterpic-green;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: darken($litterpic-green, 10%);
    }
  }
  
  .members-title-section {
    text-align: center;
    flex-grow: 1;
    
    h1 {
      color: $litterpic-green;
      font-size: 2.5rem;
      margin: 0 0 10px 0;
      font-weight: bold;
    }
    
    .members-count {
      font-size: 1.2rem;
      color: #666;
      margin: 5px 0;
      font-weight: 600;
    }
    
    .filter-description {
      font-size: 1rem;
      color: #888;
      margin: 5px 0;
      font-style: italic;
    }
  }
  
  .filter-toggle {
    background-color: #f5f5f5;
    border: 2px solid $litterpic-green;
    color: $litterpic-green;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: $litterpic-green;
      color: white;
    }
  }
}

.members-filters {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  
  .filter-row {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .filter-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 150px;
    
    label {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      font-size: 0.9rem;
    }
    
    select {
      padding: 8px 12px;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 0.9rem;
      background-color: white;
      cursor: pointer;
      transition: border-color 0.2s ease;
      height: auto;
      width: 100%;
      
      &:focus {
        outline: none;
        border-color: $litterpic-green;
      }
    }
  }
  
  .clear-filters-btn {
    display: block;
    margin: 0 auto;
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: darken(#f44336, 10%);
    }
  }
}

.members-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.member-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    border-color: $litterpic-green;
  }
  
  .member-avatar {
    text-align: center;
    margin-bottom: 15px;
    
    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid $litterpic-green;
    }
  }
  
  .member-info {
    text-align: center;
    margin-bottom: 15px;
    
    .member-name {
      font-size: 1.3rem;
      font-weight: bold;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    .member-organization {
      font-size: 1rem;
      color: $litterpic-green;
      font-style: italic;
      margin: 0 0 8px 0;
      font-weight: 500;
    }
    
    .member-joined {
      font-size: 0.9rem;
      color: #666;
      margin: 0 0 5px 0;
    }
    
    .member-weight {
      font-size: 0.9rem;
      color: #888;
      margin: 0;
      font-weight: 500;
    }
  }
  
  .member-actions {
    text-align: center;
    
    .view-profile-btn {
      background-color: $litterpic-green;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: background-color 0.2s ease;
      width: 100%;
      
      &:hover {
        background-color: darken($litterpic-green, 10%);
      }
    }
  }
}

.no-members {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  
  p {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }
  
  button {
    background-color: $litterpic-green;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: darken($litterpic-green, 10%);
    }
  }
}

// Mobile responsive styles
@media (max-width: 768px) {
  .members-page {
    padding: 15px;
  }
  
  .members-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    
    .members-title-section h1 {
      font-size: 2rem;
    }
    
    .back-button,
    .filter-toggle {
      width: 100%;
      max-width: 200px;
    }
  }
  
  .members-filters {
    .filter-row {
      flex-direction: column;
      gap: 15px;
    }
    
    .filter-group {
      width: 100%;
    }
  }
  
  .members-list {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .member-card {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .members-header .members-title-section h1 {
    font-size: 1.8rem;
  }
  
  .member-card .member-avatar img {
    width: 60px;
    height: 60px;
  }
  
  .member-card .member-info .member-name {
    font-size: 1.1rem;
  }
}
