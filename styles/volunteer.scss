@import 'colors';
@import 'fonts';

// Volunteer
.volunteer-top-paragraph {
  font-size: 1rem;
  line-height: 1.5;
}

.community-service-button {
  align-items: center;
  border-radius: .5em;
  display: flex;
  font-family: Poppins, sans-serif;
  font-size: 1rem;
  height: 2rem;
  justify-content: center;
  margin-bottom: 1rem;
  margin-top: 1rem;
  padding: .6rem;
  width: 195px;

  &:hover {
    background-color: $facebook-blue;
  }
}

// Volunteer Calendar
.event-background {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

.rbc-calendar {
  display: flex;
  margin-bottom: 1.875rem;
  width: 100%;

  .rbc-month-view {
    .rbc-day-bg {
      &.rbc-today {
        background-color: $light-green-dull;
        border-color: $litterpic-green;
        border-width: .25rem;
      }
    }
  }
}

.rbc-event-content {
  font-size: .6rem;
  height: 1.25rem;
}

.rbc-toolbar-label {
  font-family: Arial, serif;
  font-size: 1.625rem;
}

.calendar {
  display: flex;
  justify-content: center;
}


.calendar-chevron-left,
.calendar-chevron-right {
  color: $black;
  font-size: 1.25rem;
}

// Volunteer Events Table
.table {
  border-collapse: collapse;
  margin-bottom: 3rem;
  margin-top: 3rem;
  width: 100%;

  td,
  th {
    border: .06rem solid $table-gray;
    font-size: .65rem;
    max-width: 125px;
    text-align: center;
    word-wrap: break-word;
    padding-left: 10px;
    padding-right: 10px;
  }

  &.volunteer-event-organizer-photo {
    align-items: center;
    display: flex;
    justify-content: center;
  }

  .medium-column {
    width: 125px;
  }

  .narrow-column {
    width: 55px;

    .date-input-container {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }

    .date-dropdown {
      border: none;
      cursor: pointer;
      font-size: .65rem;
      font-weight: bold;
      margin: auto;
      outline: none;
      padding: 0;
      text-align: center;
    }
  }

  img {
    padding: .25rem;
  }
}

.highlight {
  background-color: $table-highlight-green;
  color: $white;
}

.form-container {
  display: grid;
  gap: .25rem;
  grid-template-columns: 200px auto;
  grid-template-rows: auto;


  label[for='numberAttending'] {
    grid-column: 1;
    grid-row: 1;
    text-align: center;
  }

  input[name='numberAttending'] {
    grid-column: 2;
    grid-row: 1;
  }

  label[for='email'] {
    grid-column: 1;
    grid-row: 2;
    text-align: center;
  }

  input[name='email'] {
    grid-column: 2;
    grid-row: 2;
  }

  label[for='name'] {
    grid-column: 1;
    grid-row: 3;
    text-align: center;
  }

  input[name='name'] {
    grid-column: 2;
    grid-row: 3;
  }

  label[for='note'] {
    grid-column: 1;
    grid-row: 4;
    text-align: center;
  }

  textarea[name='note'] {
    grid-column: 2;
    grid-row: 4;
  }
}

.form-input {
  border: 1px solid $litterpic-green;
  border-radius: 5px;
  padding: 5px;
  width: 100%;
}

.textarea-large {
  border: 1px solid $litterpic-green;
  border-radius: 5px;
  height: 60px;
  padding: 5px;
  width: 80%;
}

.input-medium {
  border: 1px solid $litterpic-green;
  border-radius: 5px;
  padding: 5px;
  width: 80%;
}

.input-small {
  border: 1px solid $litterpic-green;
  border-radius: 5px;
  padding: 5px;
  width: 50px;
}

.rsvp-buttons {
  display: flex;
  justify-content: space-between;
}

.rsvp-button {
  border-radius: .5rem;
  cursor: pointer;
  display: flex;
  font-family: Poppins, sans-serif;
  margin: 1rem auto;
  padding: .6rem;

  &.submit {
    margin-right: 1rem;
  }

  &.cancel {
    margin-left: 1rem;
  }
}

.volunteer-event-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  margin-left: 20%;
  margin-right: 20%;
}

.rsvp-thankyou {
  margin-top: 1rem;
  text-align: center;

  button {
    border-radius: .5rem;
  }
}

.cancel-rsvp-button {
  border-radius: .5rem;
  display: flex;
  font-size: .8rem;
  margin: .5rem auto;
}

.event-form {
  display: grid;
  gap: 8px;
  grid-template-rows: 1fr;
}

.form-row {
  display: grid;
  gap: 10px;
  grid-template-columns: auto 1fr;

  &:first-child {
    font-weight: bold;
    padding-right: 3rem;
    text-align: end;
  }
}


// RSVP Attendee Details
.rsvp-details-table {
  border-collapse: collapse;
  margin-left: auto;
  margin-right: auto;
  margin-top: 1rem;
  width: 99%;

  th,
  td {
    border: 1px solid $litterpic-green;
    padding: 3px 7px;
    text-align: left;
  }

  th {
    background-color: $profile-text-gray;
    color: $black;
  }

  tr {
    &:hover {
      background-color: $light-gray;
    }
  }

  button {
    background-color: $facebook-blue;
    border: 0;
    border-radius: 5px;
    color: $white;
    cursor: pointer;
    margin-top: 20px;
    padding: 10px 15px;
    transition: background-color .2s;

    &:hover {
      background-color: $facebook-blue;
    }
  }
}

.download-csv-button {
  background-color: $litterpic-green;
  border: 0;
  border-radius: .5rem;
  color: $white;
  cursor: pointer;
  font-weight: bold;
  margin-left: .5rem;
  margin-top: 2rem;
  padding: 10px 20px;
  transition: background-color .3s ease-in-out;

  &:hover {
    background-color: $facebook-blue;
  }

  &:active {
    background-color: $facebook-blue;
  }

  &:focus {
    outline: none;
  }
}

textarea {
  &.event-description-input {
    min-height: 100px;
    overflow-wrap: break-word;
    resize: none;
    width: 100%;
  }
}

.event-title {
  width: 100%;
}

.event-location {
  width: 100%;
}

.location-input {
  width: 100%;
}

.create-event-buttons {
  display: flex;
  grid-column: 1 / -1;
  grid-row: 7;
  justify-content: center;
  margin-bottom: 1rem;
  margin-top: 1rem;

  .event-submit {
    border-radius: .5rem;
    margin-right: .5rem;

    &:hover {
      background-color: $facebook-blue;
    }
  }

  .event-submit-cancel {
    border-radius: .5rem;
    margin-left: .5rem;

    &:hover {
      background-color: $facebook-blue;
    }
  }
}

.create-event-button {
  align-items: center;
  border-radius: .5rem;
  display: flex;
  font-family: Poppins, sans-serif;
  font-size: 1rem;
  height: 2rem;
  justify-content: center;
  margin-bottom: 1rem;
  margin-top: 1rem;
  padding: .6rem;
  width: 195px;

  &:hover {
    background-color: $facebook-blue;
  }

  &:disabled {
    background-color: $disabled-gray;
    cursor: not-allowed;
  }
}

@media (max-width: 76.5rem) {
  .start-time-column {
    width: 2.5rem;
  }

  .rbc-calendar {
    margin: auto auto 1.25rem;
    width: 100%;
  }

  .calendar {
    width: 100%;
  }

  .rbc-toolbar {
    button {
      font-size: 1.125rem;
    }
  }

  .create-event-buttons {
    .event-submit {
      border-radius: .5rem;
      font-weight: normal;
      margin-right: .5rem;
      width: 100px;
    }

    .event-submit-cancel {
      border-radius: .5rem;
      font-weight: normal;
      margin-left: .5rem;
      width: 100px;
    }
  }

  .rbc-toolbar-label {
    font-size: 2.25rem;
  }

  .table {
    td,
    th {
      font-size: .5rem;
      padding: .06rem;
    }

    a {
      font-size: .7rem;

    }

    .narrow-column {

      .date-input-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
      }

      .date-dropdown {
        border: none;
        cursor: pointer;
        font-size: .65rem;
        font-weight: bold;
        margin: auto;
        outline: none;
        padding: 0;
        text-align: center;
      }
    }
  }

  .cancel-rsvp-button {
    font-size: .5rem;
  }

  // RSVP Attendee Details
  .rsvp-details-table {
    border-collapse: collapse;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1rem;
    width: 99%;

    th,
    td {
      border: 1px solid $litterpic-green;
      font-size: .5rem;
      text-align: left;
    }

    th {
      background-color: $light-gray;
      color: $black;
    }

    tr {
      &:hover {
        background-color: $light-gray;
      }
    }
  }

  .download-csv-button {
    background-color: $litterpic-green;
    border: 0;
    border-radius: .5rem;
    color: $white;
    cursor: pointer;
    font-size: .5rem;
    font-weight: bold;
    margin-left: .5rem;
    margin-top: 2rem;
    padding: 10px 20px;
    transition: background-color .3s ease-in-out;

    &:hover {
      background-color: $facebook-blue;
    }

    &:active {
      background-color: $facebook-blue;
    }

    &:focus {
      outline: none;
    }
  }

}

@media (max-width: 102.25rem) and (orientation: landscape) {

  .start-time-column {
    width: 2.5rem;
  }

  .rbc-calendar {
    margin: auto auto 1.25rem;
    width: 100%;
  }

  .calendar {
    width: 100%;
  }

  .rbc-toolbar {
    button {
      font-size: 1.125rem;
    }
  }

  .rbc-toolbar-label {
    font-size: 2.5rem;
  }

  .table {
    td,
    th {
      padding: .5rem;
    }
  }
}
