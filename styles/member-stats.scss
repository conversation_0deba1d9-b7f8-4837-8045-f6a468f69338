@use 'colors';

.member-stats-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 2px solid $litterpic-green;
}

.member-stats-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.member-stats-title {
  color: $litterpic-green;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-align: center;
}

.member-count-display {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80px;
}

.loading-text {
  color: #666;
  font-size: 1.1rem;
  font-style: italic;
}

.member-count {
  text-align: center;

  &.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 10px;
    border-radius: 8px;

    &:hover {
      background-color: rgba(1, 94, 65, 0.05);
      transform: translateY(-2px);
    }
  }
}

.count-number {
  display: block;
  font-size: 3rem;
  font-weight: bold;
  color: $litterpic-green;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.count-label {
  display: block;
  font-size: 1rem;
  color: #666;
  font-weight: 500;
}

.click-hint {
  display: block;
  font-size: 0.8rem;
  color: $litterpic-green;
  font-weight: 400;
  margin-top: 5px;
  opacity: 0.8;
}

.member-filter-container {
  border-top: 1px solid #e0e0e0;
  padding-top: 1.5rem;
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.filter-inputs {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 180px;

  label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  select {
    padding: 0.5rem 0.75rem;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
    cursor: pointer;
    transition: border-color 0.2s ease;
    height: auto;
    width: 100%;
    min-width: 180px;

    &:focus {
      outline: none;
      border-color: $litterpic-green;
    }

    &:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}

.filter-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: center;
}

.reset-filter-btn {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  background-color: #f44336;
  color: white;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    background-color: darken(#f44336, 10%);
    transform: translateY(-1px);
  }
}

// Mobile responsive styles
@media (max-width: 768px) {
  .member-stats-container {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .member-stats-title {
    font-size: 1.5rem;
  }

  .count-number {
    font-size: 2.5rem;
  }

  .filter-inputs {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }

  .input-group {
    min-width: unset;
    width: 100%;
  }

  .filter-buttons {
    width: 100%;

    .reset-filter-btn {
      width: 100%;
      min-width: unset;
    }
  }
}

@media (max-width: 480px) {
  .member-stats-title {
    font-size: 1.3rem;
  }

  .count-number {
    font-size: 2rem;
  }

  .count-label {
    font-size: 0.9rem;
  }

  .filter-buttons {
    flex-direction: column;

    .reset-filter-btn {
      width: 100%;
    }
  }
}
