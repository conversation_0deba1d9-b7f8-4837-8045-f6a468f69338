/* Loading spinner styles */
.loading-spinner-container,
.initial-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  width: 100%;
  
  p {
    margin-top: 1rem;
    color: $litterpic-green;
    font-weight: 500;
    font-size: 1rem;
  }
}

.initial-loading-container {
  min-height: 300px;
  background-color: rgba(245, 245, 245, 0.5);
  border-radius: 8px;
  margin: 1rem 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(1, 94, 65, 0.2);
  border-radius: 50%;
  border-top-color: $litterpic-green;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
