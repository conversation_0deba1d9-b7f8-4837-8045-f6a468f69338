@import 'colors';
@import 'fonts';

.edit-profile-container {
  align-items: center;
  display: flex;
  justify-content: center;
  padding: 20px;

  form {
    background: $light-gray;
    border-radius: 10px;
    box-shadow: 0 0 10px $light-gray;
    margin-left: auto;
    margin-right: auto;
    max-width: 90%;
    padding: 30px;
    width: 100%;
  }
}

.edit-profile-label {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  margin-bottom: 20px;
}

.edit-profile-photo {
  align-items: center;
  display: flex;
  margin-bottom: 10px;

  img {
    border-radius: 50%;
    height: 200px;
    object-fit: cover;
    width: 200px;
  }

  &-button {
    border-radius: .5rem;
    font-size: 1rem;
    margin-bottom: 1rem;
    width: 200px;
  }
}


.edit-profile-photo-button,
.edit-profile-submit-button {
  background: $litterpic-green;
  border: 0;
  border-radius: 5px;
  color: $white;
  cursor: pointer;
  padding: 10px 20px;
  transition: background .3s ease;

  &:hover {
    background: $facebook-blue;
  }
}

.edit-profile-name-input,
.edit-profile-organization-select,
.edit-profile-new-organization-input {
  border: 1px solid $med-gray;
  border-radius: 5px;
  font-size: .9rem;
  padding-left: .5rem;
  width: 33%;
}


.edit-profile-bio-textarea {
  border: 1px solid $med-gray;
  border-radius: 5px;
  font-size: .9rem;
  line-height: 1.5;
  min-height: 200px;
  overflow-wrap: break-word;
  padding: 10px;
  resize: vertical;
  white-space: pre-wrap;
  width: 90%;
}


.edit-profile-submit-button, .edit-profile-add-organization-button {
  border-radius: 0.5rem;
  display: block;
  margin-top: 20px;
  width: 200px;
}

[disabled] {
  background: $med-gray;
  cursor: not-allowed;
}

.edit-profile-file-input {
  display: none;
}

@media (max-width: 76.5rem) {
  .edit-profile-new-organization-input {
    font-size: .75rem;
    width: 90%;
  }

  .edit-profile-container {

    form {
      max-width: 100%;
      width: 100%;
    }
  }

  .edit-profile- {
    &label {
      font-size: .75rem;
    }

    &name-input {
      font-size: .75rem;
      width: 90%;
    }

    &organization-select {
      font-size: .75rem;
      width: 90%;
    }


    &bio-textarea {
      font-size: .75rem;
      min-height: 150px;
    }
  }

  .edit-profile-submit-button {
    font-size: 1rem;
  }
}
