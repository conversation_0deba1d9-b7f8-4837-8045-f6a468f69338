@import 'colors';
@import 'fonts';

// Modern Stories Page Styles
.modern-stories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.modern-stories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: $litterpic-green;
    margin: 0;
  }
  
  .modern-create-post-button {
    background-color: $litterpic-green;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    &:hover {
      background-color: darken($litterpic-green, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    i {
      font-size: 1.25rem;
    }
  }
}

.modern-stories-intro {
  background-color: #f9f9f9;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  
  p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #333;
    margin: 0;
    max-width: 80%;
    position: relative;
    z-index: 2;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30%;
    height: 100%;
    background-image: linear-gradient(to right, rgba(249, 249, 249, 0), rgba(249, 249, 249, 1)), url('/images/litter_on_road.jpeg');
    background-size: cover;
    background-position: center;
    z-index: 1;
    opacity: 0.8;
  }
}

.modern-stories-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  
  .modern-filter-button {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 50px;
    padding: 0.5rem 1.25rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    &:hover, &.active {
      background-color: $litterpic-green;
      color: white;
      border-color: $litterpic-green;
    }
    
    i {
      font-size: 1.1rem;
    }
  }
  
  .modern-search-input {
    flex: 1;
    min-width: 200px;
    position: relative;
    
    input {
      width: 100%;
      padding: 0.5rem 1rem 0.5rem 2.5rem;
      border: 1px solid #e0e0e0;
      border-radius: 50px;
      font-size: 0.9rem;
      transition: all 0.2s ease;
      
      &:focus {
        outline: none;
        border-color: $litterpic-green;
        box-shadow: 0 0 0 2px rgba(1, 94, 65, 0.1);
      }
    }
    
    i {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 1.1rem;
    }
  }
}

// Modern Post Grid
.modern-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, 600px), 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

// Modern Post Component
.modern-post {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  }
}

.modern-post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.modern-post-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modern-post-avatar {
  position: relative;
  
  img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .ambassador-badge {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background-color: $litterpic-green;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      font-size: 14px;
    }
  }
}

.modern-post-user-info {
  display: flex;
  flex-direction: column;
}

.modern-post-username {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #333;
  transition: color 0.2s ease;
  
  &:hover {
    color: $litterpic-green;
  }
}

.modern-post-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.8rem;
  color: #777;
  margin-top: 0.25rem;
}

.modern-post-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #777;
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: $litterpic-green;
  }
  
  i {
    font-size: 0.9rem;
  }
}

.modern-post-time {
  color: #999;
}

.modern-follow-button {
  background-color: $litterpic-green;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: darken($litterpic-green, 10%);
  }
  
  &.following {
    background-color: #f0f0f0;
    color: #333;
    
    &:hover {
      background-color: #e0e0e0;
    }
  }
}

// Modern Carousel
.modern-carousel {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%; // 4:3 aspect ratio
  overflow: hidden;
  background-color: #f5f5f5;
  
  &:hover {
    .modern-carousel-indicators {
      opacity: 1;
    }
  }
}

.modern-carousel-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modern-carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.5s ease;
}

.modern-carousel-media {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.modern-carousel-indicators {
  position: absolute;
  bottom: 1rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.modern-carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.active {
    background-color: white;
    transform: scale(1.2);
  }
}

.modern-carousel-swipe-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  opacity: 0;
  animation: fadeInOut 2s ease-in-out forwards;
  pointer-events: none;
  z-index: 20;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

.modern-post-achievement {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #f0f7f4;
  color: $litterpic-green;
  font-size: 0.9rem;
  
  i {
    font-size: 1.25rem;
  }
  
  strong {
    font-weight: 700;
  }
}

.modern-post-description {
  padding: 1rem;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #333;
  max-height: 100px;
  overflow: hidden;
  position: relative;
  
  &.expanded {
    max-height: none;
  }
  
  &:not(.expanded)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
  }
  
  a {
    color: $litterpic-green;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.modern-post-expand {
  background: none;
  border: none;
  color: $litterpic-green;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  display: block;
  width: 100%;
  text-align: center;
  
  &:hover {
    text-decoration: underline;
  }
}

.modern-post-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.modern-post-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f9f9f9;
    color: $litterpic-green;
  }
  
  i {
    font-size: 1.25rem;
  }
  
  span {
    font-size: 0.9rem;
    font-weight: 500;
  }
}

.modern-load-more {
  display: flex;
  justify-content: center;
  margin: 2rem 0 4rem;
  
  button {
    background-color: white;
    border: 2px solid $litterpic-green;
    color: $litterpic-green;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: $litterpic-green;
      color: white;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Loading skeleton
.modern-post-skeleton {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  .skeleton-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .skeleton-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }
    
    .skeleton-info {
      flex: 1;
      
      .skeleton-line {
        height: 12px;
        width: 120px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 4px;
        margin-bottom: 8px;
        
        &:last-child {
          width: 80px;
          margin-bottom: 0;
        }
      }
    }
  }
  
  .skeleton-image {
    width: 100%;
    height: 0;
    padding-bottom: 75%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  .skeleton-content {
    padding: 1rem;
    
    .skeleton-line {
      height: 10px;
      width: 100%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 8px;
      
      &:nth-child(2) {
        width: 90%;
      }
      
      &:nth-child(3) {
        width: 80%;
      }
      
      &:last-child {
        width: 60%;
        margin-bottom: 0;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Responsive styles
@media (max-width: 768px) {
  .modern-stories-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    
    h1 {
      font-size: 2rem;
    }
  }
  
  .modern-stories-intro {
    padding: 1.5rem;
    
    p {
      max-width: 100%;
      font-size: 1rem;
    }
    
    &::after {
      display: none;
    }
  }
  
  .modern-stories-filters {
    flex-direction: column;
    
    .modern-search-input {
      order: -1;
    }
  }
  
  .modern-posts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .modern-post-header {
    padding: 0.75rem;
  }
  
  .modern-post-avatar img {
    width: 40px;
    height: 40px;
  }
  
  .modern-post-username {
    font-size: 0.9rem;
  }
  
  .modern-post-meta {
    font-size: 0.75rem;
  }
  
  .modern-follow-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
  
  .modern-carousel {
    padding-bottom: 100%; // 1:1 aspect ratio on mobile
  }
  
  .modern-post-description {
    font-size: 0.9rem;
    padding: 0.75rem;
  }
}
