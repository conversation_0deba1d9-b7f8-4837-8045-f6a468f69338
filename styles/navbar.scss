@import 'colors';
@import 'fonts';

// Nav Bar
.nav-bar {
  align-items: center;
  display: flex;
  font-family: Rubik, Times, sans-serif;
  font-size: 2rem;
  font-weight: bolder;
  height: auto;
  justify-content: center;
  padding-bottom: .6rem;
  padding-top: .3rem;
  width: 100%;

  a,
  .nav-link {
    color: $black;
    font-size: 1.75rem;
    font-weight: lighter;
    margin-left: .5rem;
    margin-right: .5rem;
    text-decoration: none;
    transition: color .1s ease;

    &:hover {
      color: $table-highlight-green;
    }
  }

  .nav-link {
    font-size: 1.5rem;
  }

  .nav-links {
    align-items: center;
    display: flex;
    position: relative;

    a {
      background-color: transparent;
      margin-right: 1.5rem;

      button {
        background-color: transparent;
        color: $black;
        font-family: Rubik, Times, sans-serif;
        font-size: 1.5rem;
      }
    }
  }

  .nav-toggle {
    display: none;
  }

  &.mobile-nav {
    .nav-links {
      align-items: flex-start;
      display: flex;
      flex-direction: column;
      margin-top: 1rem;

      a {
        margin: 0 0 .5rem;
      }
    }
  }
}

.hide {
  display: none;
}

@media (max-width: 76.5rem) {
  .nav-bar {
    a {
      font-size: 1rem;
    }
  }

  .nav-links:hover {
    background-color: red;
  }


  .nav-bar {
    .nav-links {
      background-color: $gray;
      display: none;
      flex-direction: column;
      margin-top: .5rem;
      padding: .5rem;
      position: absolute;
      right: .5rem;
      top: 7%;
      z-index: 100;


    }

    .nav-toggle {
      align-items: center;
      cursor: pointer;
      display: flex;
      margin-bottom: 4.5rem;
      margin-right: .5rem;
      margin-left: 1rem;
      position: absolute;
      right: .5rem;

      .icon {
        align-items: flex-start;
        display: flex;
        justify-content: flex-start;

        svg {
          fill: currentColor;
          height: 2.5rem;
          margin-bottom: 3rem;
          width: 2.5rem;
        }
      }
    }
  }

  .nav-bar.mobile-nav .nav-links.show-navlinks {
    display: flex;
    width: 100%; /* Ensure full width */
    text-align: center; /* Center all text */

    div, a, .nav-link {
      width: 100%; /* Full width for all links */
      text-align: center; /* Center all text */
      padding: 12px 0; /* Consistent padding */
      border-bottom: 1px solid rgba(0,0,0,0.1); /* Separator between links */
      display: flex;
      justify-content: center;
      align-items: center;

      &:last-child {
        border-bottom: none; /* No border for last item */
      }
    }

    /* Fix for the nav-links-desktop container */
    .nav-links-desktop {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin: 0;

      div, a, .nav-link {
        width: 100%;
        text-align: center;
        display: flex;
        justify-content: center;
        padding: 12px 0;
        margin: 0;
      }
    }
  }

  .hide {
    display: grid;
  }

  .nav-links-desktop {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .nav-link, div, a {
      color: $black;
      cursor: pointer;
      font-size: 1rem;
      font-weight: lighter;
      margin: 0;
      padding: 12px 0;
      text-decoration: none;
      transition: color .1s ease;
      text-align: center; /* Center text */
      width: 100%; /* Full width */
      display: flex;
      justify-content: center;
      align-items: center;

      &:hover {
        color: $table-highlight-green;
      }
    }
  }

}

@media (max-width: 102.25rem) and (orientation: landscape) {
  .nav-links-desktop {
    .nav-link {
      color: $black;
      cursor: pointer;
      font-size: 1.5rem;
      font-weight: lighter;
      margin-left: 0;
      margin-right: .5rem;
      text-decoration: none;
      transition: color .1s ease;

      &:hover {
        color: $table-highlight-green;
      }
    }
  }

  .nav-bar {
    .nav-links {
      top: 15%;
    }
  }
}
