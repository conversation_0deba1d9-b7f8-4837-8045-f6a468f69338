name: Delete branch after merge

on:
  pull_request:
    types:
      - closed

jobs:
  delete_branch:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true && github.event.pull_request.head.ref != 'master' && github.event.pull_request.head.ref != 'main'

    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.REPO_ACCESS_TOKEN }}

      - name: Delete merged branch
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git push origin --delete ${GITHUB_HEAD_REF}
