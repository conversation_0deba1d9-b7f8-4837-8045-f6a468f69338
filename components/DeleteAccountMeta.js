import React from "react";
import Head from "next/head";

const DeleteAccountMeta = () => (
    <Head>
        <title>Delete Account - LitterPic</title>
        <meta name="description" content="Delete your LitterPic account and remove all associated data." />
        <meta name="robots" content="noindex, nofollow" />
        <meta property="og:title" content="Delete Account - LitterPic" />
        <meta property="og:description" content="Delete your LitterPic account and remove all associated data." />
        <meta property="og:image" content="/images/walking_away.jpeg" />
        <meta property="og:url" content="https://litterpic.org/delete-account" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Delete Account - LitterPic" />
        <meta name="twitter:description" content="Delete your LitterPic account and remove all associated data." />
        <meta name="twitter:image" content="/images/walking_away.jpeg" />
        <meta name="twitter:url" content="https://litterpic.org/delete-account" />
        <meta name="keywords" content="delete account, remove data, LitterPic" />
        <meta name="author" content="LitterPic Inc." />
    </Head>
);

export default DeleteAccountMeta;
