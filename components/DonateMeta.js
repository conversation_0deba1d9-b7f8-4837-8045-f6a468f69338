import React from "react";
import Head from "next/head";

const DonateMeta = () => (
    <Head>
        <title>Donate to LitterPic</title>
        <meta name="description"
              content="Support LitterPic's mission to create a litter-free world. Your donation helps us empower individuals, facilitate cleanups, and share impactful stories. Join us in making a cleaner, safer planet."/>
        <meta name="robots" content="index, follow"/>
        <link rel="icon" href="/favicon.ico"/>
        <link rel="canonical" href="https://litterpic.org/donate"/>

        <meta property="og:title" content="Donate to LitterPic"/>
        <meta property="og:description"
              content="Support LitterPic's mission to create a litter-free world. Your donation helps us empower individuals, facilitate cleanups, and share impactful stories. Join us in making a cleaner, safer planet."/>
        <meta property="og:image" content="https://litterpic.org/images/501(c)(3).webp"/>
        <meta property="og:url" content="https://litterpic.org/donate"/>
        <meta property="og:type" content="website"/>

        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="twitter:title" content="Donate to LitterPic"/>
        <meta name="twitter:description"
              content="Support LitterPic's mission to create a litter-free world. Your donation helps us empower individuals, facilitate cleanups, and share impactful stories. Join us in making a cleaner, safer planet."/>
        <meta name="twitter:image" content="https://litterpic.org/images/501(c)(3).webp"/>
        <meta name="twitter:url" content="https://litterpic.org/donate"/>

        <meta name="keywords"
              content="litter, litterpicking, litter collection, community cleanups, environmental conservation, inspiring stories"/>
        <meta name="author" content="LitterPic Inc."/>
    </Head>
);

export default DonateMeta;
