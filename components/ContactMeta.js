import React from "react";
import Head from "next/head";

const ContactMeta = () => (
    <Head>
        <title>Contact LitterPic</title>
        <meta name="description" content="Get in touch with LitterPic for any inquiries or support." />
        <meta name="robots" content="index, follow" />
        <meta property="og:title" content="Contact LitterPic" />
        <meta property="og:description" content="Get in touch with LitterPic for any inquiries or support." />
        <meta property="og:image" content="https://litterpic.org/images/contact_banner.webp" />
        <meta property="og:url" content="https://litterpic.org/contact" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Contact LitterPic" />
        <meta name="twitter:description" content="Get in touch with LitterPic for any inquiries or support." />
        <meta name="twitter:image" content="https://litterpic.org/images/contact_banner.webp" />
        <meta name="twitter:url" content="https://litterpic.org/contact" />
        <meta name="keywords" content="contact, support, inquiries, LitterPic" />
        <meta name="author" content="LitterPic Inc." />
    </Head>
);

export default ContactMeta;
