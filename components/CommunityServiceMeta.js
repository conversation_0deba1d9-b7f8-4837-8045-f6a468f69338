import React from "react";
import Head from "next/head";

const CommunityServiceMeta = () => (
    <Head>
        <title>LitterPic supports Community Service</title>
        <meta name="description"
              content="Earn community service hours with LitterPic by cleaning littered areas and sharing your efforts. Ensure safe cleanups, post before-and-after photos, and request <NAME_EMAIL>. We verify all submissions for authenticity and provide proof of service. Join us in making communities cleaner!"/>
        <meta name="robots" content="index, follow"/>
        <link rel="canonical" href="https://litterpic.org/community_service_hours"/>

        <meta property="og:title" content="LitterPic"/>
        <meta property="og:description"
              content="Earn community service hours with LitterPic by cleaning littered areas and sharing your efforts. Ensure safe cleanups, post before-and-after photos, and request <NAME_EMAIL>. We verify all submissions for authenticity and provide proof of service. Join us in making communities cleaner!"/>
        <meta property="og:image"
              content="https://firebasestorage.googleapis.com/v0/b/litterpic-fa0bb.appspot.com/o/userPosts%2FgiV8aaisrLNCA6pQYSOS%2F1.webp?alt=media&token=a62f6ea3-2b54-4930-aa91-4e4d2076c39e"/>
        <meta property="og:url" content="https://litterpic.org/community_service_hours"/>
        <meta property="og:type" content="website"/>

        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="twitter:title" content="LitterPic"/>
        <meta name="twitter:description"
              content="Earn community service hours with LitterPic by cleaning littered areas and sharing your efforts. Ensure safe cleanups, post before-and-after photos, and request <NAME_EMAIL>. We verify all submissions for authenticity and provide proof of service. Join us in making communities cleaner!"/>
        <meta name="twitter:image"
              content="https://firebasestorage.googleapis.com/v0/b/litterpic-fa0bb.appspot.com/o/userPosts%2FgiV8aaisrLNCA6pQYSOS%2F1.webp?alt=media&token=a62f6ea3-2b54-4930-aa91-4e4d2076c39e"/>
        <meta name="twitter:url" content="https://litterpic.org/community_service_hours"/>

        <meta name="keywords"
              content="litter, litterpicking, litter collection, community cleanups, environmental conservation, inspiring stories"/>
        <meta name="author" content="LitterPic Inc."/>
    </Head>
);

export default CommunityServiceMeta;
