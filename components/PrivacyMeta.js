import React from "react";
import Head from "next/head";

const PrivacyMeta = () => (
    <Head>
        <title>LitterPic Privacy Policy</title>
        <meta name="description" content="Learn about LitterPic's privacy policy and how we protect your data." />
        <meta name="robots" content="index, follow" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="canonical" href="https://litterpic.org/privacy" />

        <meta property="og:title" content="Privacy Policy - LitterPic" />
        <meta property="og:description" content="Learn about LitterPic's privacy policy and how we protect your data." />
        <meta property="og:image" content="https://litterpic.org/images/privacy_policy_banner.webp" />
        <meta property="og:url" content="https://litterpic.org/privacy" />
        <meta property="og:type" content="website" />

        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Privacy Policy - LitterPic" />
        <meta name="twitter:description" content="Learn about LitterPic's privacy policy and how we protect your data." />
        <meta name="twitter:image" content="https://litterpic.org/images/privacy_policy_banner.webp" />
        <meta name="twitter:url" content="https://litterpic.org/privacy" />

        <meta name="keywords" content="privacy policy, data protection, user privacy, data security, online privacy" />
        <meta name="author" content="LitterPic Inc." />
    </Head>
);

export default PrivacyMeta;
