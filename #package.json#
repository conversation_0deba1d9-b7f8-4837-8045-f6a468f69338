{
  "name": "litterpic_org",
  "version": "1.0.0",
  "description": "A Next.js website for LitterPic.org",
  "main": "index.js",
  "scripts": {
    "dev": "concurrently \"npm run compile-sass\" \"next\"",
    "compile-sass": "echo 'Compiling SASS...' && sass styles/styles.scss styles/styles.css --watch",
    "build": "next build",
    "start": "next start",
    "test": "echo \"Error: no test specified\" && exit 1",
    "export": "next export",
    "lint": "sass-lint -v",
    "lint:fix": "sass-lint-auto-fix",
    "clean": "rm -rf out & npm ci"
  },
  "keywords": [],
  "author": "Your Name",
  "license": "ISC",
  "dependencies": {
    "@playwright/test": "^1.37.1",
    "@react-google-maps/api": "^2.18.1",
    "@sendgrid/mail": "^7.7.0",
    "aws-sdk": "^2.1450.0",
    "axios": "^1.5.0",
    "cors": "^2.8.5",
    "firebase": "^9.23.0",
    "firebase-admin": "^12.0.0",
    "kill-port": "^2.0.1",
    "lodash": "^4.17.21",
    "moment": "^2.29.4",
    "next": "^13.4.4",
    "playwright": "^1.37.1",
    "react": "^18.2.0",
    "react-big-calendar": "^1.8.0",
    "react-bootstrap": "^2.7.4",
    "react-dom": "^18.2.0",
    "react-google-places-autocomplete": "^4.0.1",
    "react-icons": "^4.9.0",
    "react-lazy-load-image-component": "^1.5.6",
    "react-masonry-css": "^1.0.16",
    "react-places-autocomplete": "^7.3.0",
    "react-responsive-carousel": "^3.2.23",
    "react-router-dom": "^6.14.2",
    "react-slick": "^0.29.0",
    "react-swipeable": "^7.0.1",
    "react-toastify": "^9.1.3",
    "slick-carousel": "^1.8.1"
  },
  "devDependencies": {
    "concurrently": "^8.2.2",
    "sass": "^1.63.4",
    "sass-lint-auto-fix": "^0.21.2"
  }

  "dev": "concurrently \"npm run compile-sass\" \"next\"",
  "compile-sass": "echo 'Compiling SASS...' && sass styles/styles.scss styles/styles.css --watch
}
