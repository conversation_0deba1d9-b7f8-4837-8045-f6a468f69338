{"name": "litterpic_org", "version": "1.0.0", "description": "A Next.js website for LitterPic.org", "main": "index.js", "scripts": {"dev": "concurrently \"npm run compile-sass\" \"next\"", "compile-sass": "echo 'Compiling SASS...' && sass styles/styles.scss styles/styles.css --watch", "build": "next build", "start": "next start", "test": "vitest", "test:debug": "PWDEBUG=1 playwright test", "export": "next export", "lint": "sass-lint -v", "lint:fix": "sass-lint-auto-fix", "clean": "rm -rf out & npm ci", "deploy": "npm run build && vercel --prod", "deploy:preview": "npm run build && vercel"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@react-google-maps/api": "^2.18.1", "@sendgrid/mail": "^7.7.0", "autoprefixer": "^10.4.20", "aws-sdk": "^2.1450.0", "axios": "^1.8.3", "cors": "^2.8.5", "date-fns": "^3.6.0", "dompurify": "^3.1.7", "firebase": "^9.23.0", "firebase-admin": "^12.0.0", "kill-port": "^2.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "^14.2.0", "postcss": "^8.4.47", "postmark": "^4.0.5", "react": "^18.3.0", "react-big-calendar": "^1.8.0", "react-bootstrap": "^2.7.4", "react-dom": "^18.3.0", "react-google-places-autocomplete": "^4.0.1", "react-icons": "^4.9.0", "react-lazy-load-image-component": "^1.5.6", "react-masonry-css": "^1.0.16", "react-places-autocomplete": "^7.3.0", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.14.2", "react-slick": "^0.29.0", "react-swipeable": "^7.0.1", "react-toastify": "^9.1.3", "sharp": "^0.34.1", "slick-carousel": "^1.8.1", "swr": "^2.3.3", "tailwindcss": "^3.4.13", "tcp-port-used": "^1.0.2", "webp-converter": "^2.3.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "concurrently": "^8.2.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.0.0", "sass": "^1.63.4", "sass-lint-auto-fix": "^0.21.2", "vitest": "^3.0.9"}}